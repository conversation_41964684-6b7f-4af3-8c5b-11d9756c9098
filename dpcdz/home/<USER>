from django.db import models
from django.contrib.auth.models import User
import os

WILAYA_CHOICES = (
    ('01', 'أدرار'), ('02', 'الشلف'), ('03', 'الأغواط'), ('04', 'أم البواقي'),
    ('05', 'باتنة'), ('06', 'بجاية'), ('07', 'بسكرة'), ('08', 'بشار'),
    ('09', 'البليدة'), ('10', 'البويرة'), ('11', 'تمنراست'), ('12', 'تبسة'),
    ('13', 'تلمسان'), ('14', 'تيارت'), ('15', 'تيزي وزو'), ('16', 'الجزائر العاصمة'),
    ('17', 'الجلفة'), ('18', 'جيجل'), ('19', 'سطيف'), ('20', 'سعيدة'),
    ('21', 'سكيكدة'), ('22', 'سيدي بلعباس'), ('23', 'عنابة'), ('24', 'قالمة'),
    ('25', 'قسنطينة'), ('26', 'المدية'), ('27', 'مستغانم'), ('28', 'المسيلة'),
    ('29', 'معسكر'), ('30', 'ورقلة'), ('31', 'وهران'), ('32', 'البيض'),
    ('33', 'إليزي'), ('34', 'برج بوعريريج'), ('35', 'بومرداس'), ('36', 'الطارف'),
    ('37', 'تندوف'), ('38', 'تيسمسيلت'), ('39', 'الوادي'), ('40', 'خنشلة'),
    ('41', 'سوق أهراس'), ('42', 'تيبازة'), ('43', 'ميلة'), ('44', 'عين الدفلى'),
    ('45', 'النعامة'), ('46', 'عين تموشنت'), ('47', 'غرداية'), ('48', 'غليزان'),
    ('49', 'تيميمون'), ('50', 'برج باجي مختار'), ('51', 'أولاد جلال'),
    ('52', 'بني عباس'), ('53', 'عين صالح'), ('54', 'عين قزام'),
    ('55', 'تقرت'), ('56', 'جانت'), ('57', 'المغير'), ('58', 'المنيعة')
)

# Original fire-related models
class GeneralFireData(models.Model):
    date = models.DateField()
    intervening_unit = models.CharField(max_length=100)
    fire_type = models.CharField(max_length=100)
    number_of_fires = models.PositiveIntegerField()
    number_of_interventions = models.PositiveIntegerField()
    number_of_injured = models.PositiveIntegerField()
    number_of_deaths = models.PositiveIntegerField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Fire Data - {self.date} - {self.intervening_unit} - {self.fire_type}"

class ResidentialFireData(models.Model):
    date = models.DateField()
    intervening_unit = models.CharField(max_length=100)
    family_first = models.BooleanField(default=False)
    family_second = models.BooleanField(default=False)
    family_third = models.BooleanField(default=False)
    family_fourth = models.BooleanField(default=False)
    number_of_fires = models.PositiveIntegerField()
    number_of_interventions = models.PositiveIntegerField()
    number_of_injured = models.PositiveIntegerField()
    number_of_deaths = models.PositiveIntegerField()
    ambulances = models.PositiveIntegerField()
    fire_trucks = models.PositiveIntegerField()
    mechanical_ladders = models.PositiveIntegerField()
    other_resources = models.PositiveIntegerField()
    intervention_duration = models.CharField(max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Residential Fire Data - {self.date} - {self.intervening_unit}"

class PublicAreaFireData(models.Model):
    date = models.DateField()
    intervening_unit = models.CharField(max_length=100)
    number_of_fires = models.PositiveIntegerField()
    number_of_interventions = models.PositiveIntegerField()
    number_of_injured = models.PositiveIntegerField()
    number_of_deaths = models.PositiveIntegerField()
    institution_name = models.CharField(max_length=200)
    institution_type = models.CharField(max_length=100)
    institution_category = models.CharField(max_length=100)
    ambulances = models.PositiveIntegerField()
    fire_trucks = models.PositiveIntegerField()
    mechanical_ladders = models.PositiveIntegerField()
    other_resources = models.PositiveIntegerField()
    intervention_duration = models.CharField(max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Public Area Fire Data - {self.date} - {self.intervening_unit} - {self.institution_name}"

class ForestAgriculturalFireData(models.Model):
    date = models.DateField()
    municipality = models.CharField(max_length=100)
    intervening_unit = models.CharField(max_length=100)
    fire_type = models.CharField(max_length=100)
    number_of_fires = models.PositiveIntegerField()
    number_of_interventions = models.PositiveIntegerField()
    losses_hectare = models.PositiveIntegerField(null=True, blank=True)
    losses_are = models.PositiveIntegerField(null=True, blank=True)
    losses_square_meter = models.PositiveIntegerField(null=True, blank=True)
    losses_count = models.PositiveIntegerField(null=True, blank=True)
    loss_type = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Forest/Agricultural Fire Data - {self.date} - {self.municipality} - {self.fire_type}"

class InstitutionalFireData(models.Model):
    date = models.DateField()
    intervening_unit = models.CharField(max_length=100)
    incident_type = models.CharField(max_length=100)
    operations_count = models.PositiveIntegerField()
    activity_nature = models.CharField(max_length=200)
    institution_name = models.CharField(max_length=200)
    activity = models.CharField(max_length=200)
    class_type = models.CharField(max_length=100)
    year_pii = models.BooleanField(default=False)
    year_edd = models.BooleanField(default=False)
    zone_zr = models.BooleanField(default=False)
    zone_zu = models.BooleanField(default=False)
    zone_za = models.BooleanField(default=False)
    zone_zi = models.BooleanField(default=False)
    injured_count = models.PositiveIntegerField()
    deaths_count = models.PositiveIntegerField()
    ambulances = models.PositiveIntegerField()
    fire_trucks = models.PositiveIntegerField()
    mechanical_ladders = models.PositiveIntegerField()
    other_resources = models.PositiveIntegerField()
    intervention_duration = models.CharField(max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Institutional Fire Data - {self.date} - {self.intervening_unit} - {self.institution_name}"

class MiscOperationsData(models.Model):
    date = models.DateField()
    intervening_unit = models.CharField(max_length=100)
    operation_type = models.CharField(max_length=100)
    number_of_operations = models.PositiveIntegerField()
    number_of_interventions = models.PositiveIntegerField()
    number_of_rescuers = models.PositiveIntegerField(null=True, blank=True)
    number_of_deaths = models.PositiveIntegerField(null=True, blank=True)
    number_of_women = models.PositiveIntegerField(null=True, blank=True)
    number_of_men = models.PositiveIntegerField(null=True, blank=True)
    number_of_children = models.PositiveIntegerField(null=True, blank=True)
    # Fields for interventions without work (vehicles/resources)
    cars = models.PositiveIntegerField(null=True, blank=True)
    trucks = models.PositiveIntegerField(null=True, blank=True)
    other_resources = models.PositiveIntegerField(null=True, blank=True)
    # Fields for exceptional operations (resources)
    ambulances = models.PositiveIntegerField(null=True, blank=True)
    fire_trucks = models.PositiveIntegerField(null=True, blank=True)
    mechanical_ladders = models.PositiveIntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Misc Operations Data - {self.date} - {self.intervening_unit} - {self.operation_type}"

class TrafficAccident(models.Model):
    date = models.DateField(verbose_name='التاريخ')
    unit = models.CharField(max_length=100, verbose_name='الوحدة المتدخلة')
    accident_type = models.CharField(max_length=100, verbose_name='نوع الحادث')
    accident_nature = models.CharField(max_length=100, verbose_name='طبيعة الحادث')
    accidents_count = models.PositiveIntegerField(default=0, verbose_name='عدد الحوادث')
    operations_count = models.PositiveIntegerField(default=0, verbose_name='عدد العمليات')
    road_type = models.CharField(max_length=100, verbose_name='نوع الطريق')
    driver_age = models.CharField(max_length=100, verbose_name='فئة السائقين')

    # Human losses
    casualties_men = models.PositiveIntegerField(default=0, verbose_name='عدد الجرحى رجال')
    casualties_women = models.PositiveIntegerField(default=0, verbose_name='عدد الجرحى نساء')
    casualties_children = models.PositiveIntegerField(default=0, verbose_name='عدد الجرحى أطفال')
    total_casualties = models.PositiveIntegerField(default=0, verbose_name='مجموع الجرحى')

    fatalities_men = models.PositiveIntegerField(default=0, verbose_name='عدد الوفيات رجال')
    fatalities_women = models.PositiveIntegerField(default=0, verbose_name='عدد الوفيات نساء')
    fatalities_children = models.PositiveIntegerField(default=0, verbose_name='عدد الوفيات أطفال')
    total_fatalities = models.PositiveIntegerField(default=0, verbose_name='مجموع الوفيات')

    # Material losses
    fuel_cars = models.PositiveIntegerField(default=0, verbose_name='سيارات وقود')
    lpg_cars = models.PositiveIntegerField(default=0, verbose_name='سيارات غاز مميع')
    trucks = models.PositiveIntegerField(default=0, verbose_name='شاحنات')
    buses = models.PositiveIntegerField(default=0, verbose_name='حافلات')
    motorcycles = models.PositiveIntegerField(default=0, verbose_name='دراجات')
    tractors = models.PositiveIntegerField(default=0, verbose_name='جرارات')
    directed_transport = models.PositiveIntegerField(default=0, verbose_name='النقل موجه')
    other_vehicles = models.PositiveIntegerField(default=0, verbose_name='أخرى')

    # Time slots
    time_06_09 = models.PositiveIntegerField(default=0, verbose_name='06:00 - 09:00')
    time_09_12 = models.PositiveIntegerField(default=0, verbose_name='09:00 - 12:00')
    time_12_14 = models.PositiveIntegerField(default=0, verbose_name='12:00 - 14:00')
    time_14_16 = models.PositiveIntegerField(default=0, verbose_name='14:00 - 16:00')
    time_16_20 = models.PositiveIntegerField(default=0, verbose_name='16:00 - 20:00')
    time_20_00 = models.PositiveIntegerField(default=0, verbose_name='20:00 - 00:00')
    time_00_06 = models.PositiveIntegerField(default=0, verbose_name='00:00 - 06:00')

    # Days of week
    sunday = models.PositiveIntegerField(default=0, verbose_name='الأحد')
    monday = models.PositiveIntegerField(default=0, verbose_name='الإثنين')
    tuesday = models.PositiveIntegerField(default=0, verbose_name='الثلاثاء')
    wednesday = models.PositiveIntegerField(default=0, verbose_name='الأربعاء')
    thursday = models.PositiveIntegerField(default=0, verbose_name='الخميس')
    friday = models.PositiveIntegerField(default=0, verbose_name='الجمعة')
    saturday = models.PositiveIntegerField(default=0, verbose_name='السبت')

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'حادث مرور'
        verbose_name_plural = 'حوادث المرور'
        ordering = ['-date']

    def __str__(self):
        return f"Traffic Accident - {self.date} - {self.unit} - {self.accident_type}"

# User management models
class UserProfile(models.Model):
    ROLES = (
        ('admin', 'مدير النظام'),
        ('wilaya_manager', 'مدير الولاية'),
        ('unit_manager', 'مدير الوحدة'),
        ('unit_coordinator', 'مركز تنسيق العمليات الوحدة')
    )

    user = models.OneToOneField(User, on_delete=models.CASCADE)
    wilaya = models.CharField(max_length=2, choices=WILAYA_CHOICES)
    role = models.CharField(max_length=20, choices=ROLES)
    intervention_units = models.ManyToManyField('InterventionUnit', blank=True, verbose_name='الوحدات المخصصة')

    def __str__(self):
        return f"{self.user.username} - {self.get_wilaya_display()} ({self.role})"

class InterventionUnit(models.Model):
    wilaya = models.CharField(max_length=2, choices=WILAYA_CHOICES)
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True)
    is_default = models.BooleanField(default=False)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['wilaya', 'name']

    def __str__(self):
        return f"{self.code} - {self.name} ({self.get_wilaya_display()})"

    @classmethod
    def get_souk_ahras_defaults(cls):
        return [
            {'name': 'الوحدة الرئيسية سوق أهراس', 'code': 'SA-MAIN'},
            {'name': 'المركز المتقدم طريق رقم 16', 'code': 'SA-ADV16'},
            {'name': 'المركز المتقدم برال صالح', 'code': 'SA-ADVBS'},
            {'name': 'الوحدة الثانوية سدراتة', 'code': 'SA-SEDR'},
            {'name': 'الوحدة الثانوية مداوروش', 'code': 'SA-MEDA'},
            {'name': 'الوحدة الثانوية المشروحة', 'code': 'SA-MASH'},
            {'name': 'الوحدة الثانوية بئربوحوش', 'code': 'SA-BIRB'},
            {'name': 'الوحدة الثانوية أم العضائم', 'code': 'SA-OUMA'},
            {'name': 'الوحدة الثانوية المراهنة', 'code': 'SA-MARA'},
            {'name': 'الوحدة الثانوية الحدادة', 'code': 'SA-HADA'},
            {'name': 'الوحدة الثانوية أولاد إدريس', 'code': 'SA-OUID'},
            {'name': 'الوحدة الثانوية عين الزانة', 'code': 'SA-AINZ'}
        ]

class CoordinationCenterForestFire(models.Model):
    telegram_number = models.PositiveIntegerField(verbose_name='رقم البرقية')
    date = models.DateField(verbose_name='التاريخ')
    intervention_time = models.CharField(max_length=20, verbose_name='ساعة التدخل')  # Format: "X سا Y د"
    intervening_unit = models.CharField(max_length=100, verbose_name='الوحدة المتدخلة')
    municipality = models.CharField(max_length=100, verbose_name='البلدية')
    location_name = models.CharField(max_length=200, verbose_name='المكان المسمى')
    operation_duration = models.CharField(max_length=20, verbose_name='مدة العملية')  # Format: "X سا Y د"
    intervention_means = models.TextField(verbose_name='الوسائل المتدخلة')
    loss_nature = models.TextField(verbose_name='طبيعة الخسائر')
    losses_hectare = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الخسائر بالهكتار')
    losses_are = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الخسائر بالآر')
    losses_square_meter = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الخسائر بالمتر مربع')
    other_loss_nature = models.TextField(blank=True, null=True, verbose_name='طبيعة الخسائر الأخرى')
    other_loss_count = models.PositiveIntegerField(default=0, verbose_name='عدد الخسائر')

    # Fire control and casualties information
    fire_control_status = models.CharField(max_length=50, verbose_name='وضعية التحكم في الحريق',
                                         choices=[
                                             ('أخمد نهائيا', 'أخمد نهائيا'),
                                             ('مسيطر عليه', 'مسيطر عليه'),
                                             ('خارج عن السيطرة', 'خارج عن السيطرة')
                                         ], default='أخمد نهائيا')
    injured_count = models.PositiveIntegerField(default=0, verbose_name='عدد الجرحى')
    deaths_count = models.PositiveIntegerField(default=0, verbose_name='عدد الوفيات')

    # Evacuation information
    evacuated_families_count = models.PositiveIntegerField(default=0, verbose_name='عدد العائلات الذين تم إجلاءهم')
    evacuated_people_count = models.PositiveIntegerField(default=0, verbose_name='عدد الأشخاص الذين تم إجلاءهم')
    evacuation_locations = models.TextField(blank=True, null=True, verbose_name='أماكن إجلاء العائلات بالتفصيل')
    family_care_measures = models.TextField(blank=True, null=True, verbose_name='الإجراءات المتخذة للتكفل بالعائلات المتضررة')

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Forest Fire - {self.telegram_number} - {self.date} - {self.municipality}"

    class Meta:
        verbose_name = "حريق غابات - مركز التنسيق"
        verbose_name_plural = "حرائق الغابات - مركز التنسيق"


class CoordinationCenterCropFire(models.Model):
    telegram_number = models.PositiveIntegerField(verbose_name='رقم البرقية')
    date = models.DateField(verbose_name='التاريخ')
    intervention_time = models.CharField(max_length=20, verbose_name='ساعة التدخل')  # Format: "X سا Y د"
    intervening_unit = models.CharField(max_length=100, verbose_name='الوحدة المتدخلة')
    municipality = models.CharField(max_length=100, verbose_name='البلدية')
    location_name = models.CharField(max_length=200, verbose_name='المكان المسمى')
    operation_duration = models.CharField(max_length=20, verbose_name='مدة العملية')  # Format: "X سا Y د"
    intervention_means = models.TextField(verbose_name='الوسائل المتدخلة')
    loss_nature = models.TextField(verbose_name='طبيعة الخسائر')
    losses_hectare = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الخسائر بالهكتار')
    losses_are = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الخسائر بالآر')
    losses_square_meter = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='الخسائر بالمتر مربع')
    other_loss_nature = models.TextField(blank=True, null=True, verbose_name='طبيعة الخسائر الأخرى')
    other_loss_count = models.PositiveIntegerField(default=0, verbose_name='عدد الخسائر')

    # Fire control and casualties information
    fire_control_status = models.CharField(max_length=50, verbose_name='وضعية التحكم في الحريق',
                                         choices=[
                                             ('أخمد نهائيا', 'أخمد نهائيا'),
                                             ('مسيطر عليه', 'مسيطر عليه'),
                                             ('خارج عن السيطرة', 'خارج عن السيطرة')
                                         ], default='أخمد نهائيا')
    injured_count = models.PositiveIntegerField(default=0, verbose_name='عدد الجرحى')
    deaths_count = models.PositiveIntegerField(default=0, verbose_name='عدد الوفيات')

    # Evacuation information
    evacuated_families_count = models.PositiveIntegerField(default=0, verbose_name='عدد العائلات الذين تم إجلاءهم')
    evacuated_people_count = models.PositiveIntegerField(default=0, verbose_name='عدد الأشخاص الذين تم إجلاءهم')
    evacuation_locations = models.TextField(blank=True, null=True, verbose_name='أماكن إجلاء العائلات بالتفصيل')
    family_care_measures = models.TextField(blank=True, null=True, verbose_name='الإجراءات المتخذة للتكفل بالعائلات المتضررة')

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Crop Fire - {self.telegram_number} - {self.date} - {self.municipality}"


# Daily Unit Count Models
class DailyUnitCount(models.Model):
    """Main model for daily unit count records"""
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')
    date = models.DateField(verbose_name='التاريخ')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['unit', 'date']
        verbose_name = 'التعداد الصباحي للوحدة'
        verbose_name_plural = 'التعداد الصباحي للوحدات'
        ordering = ['-date', 'unit']

    def __str__(self):
        return f"التعداد الصباحي - {self.unit.name} - {self.date}"


class PersonnelCount(models.Model):
    """Model for personnel (human resources) count"""
    STATUS_CHOICES = (
        ('present', 'حاضر'),
        ('absent', 'غائب'),
        ('on_mission', 'في مهمة')
    )

    JOB_FUNCTION_CHOICES = (
        ('driver', 'سائق'),
        ('crew_chief', 'رئيس عدد'),
        ('agent', 'عون'),
        ('specialist', 'متخصص'),
        ('officer', 'ضابط'),
        ('admin', 'إداري')
    )

    SPECIALIZATION_CHOICES = (
        ('general', 'عام'),
        ('medical', 'طبي'),
        ('technical', 'تقني'),
        ('rescue', 'إنقاذ'),
        ('diving', 'غطس'),
        ('cynotechnical', 'سينوتقنية'),
        ('communications', 'اتصالات'),
        ('maintenance', 'صيانة')
    )

    EXPERIENCE_LEVEL_CHOICES = (
        ('beginner', 'مبتدئ'),
        ('intermediate', 'متوسط'),
        ('advanced', 'متقدم'),
        ('expert', 'خبير')
    )

    daily_count = models.ForeignKey(DailyUnitCount, on_delete=models.CASCADE, related_name='personnel')
    registration_number = models.CharField(max_length=20, verbose_name='رقم القيد')
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')
    rank = models.CharField(max_length=50, verbose_name='الرتبة', blank=True, null=True)
    position = models.CharField(max_length=50, verbose_name='المنصب', blank=True, null=True)
    job_function = models.CharField(max_length=20, choices=JOB_FUNCTION_CHOICES, default='agent', verbose_name='الوظيفة')
    specialization = models.CharField(max_length=20, choices=SPECIALIZATION_CHOICES, default='general', verbose_name='التخصص')
    experience_level = models.CharField(max_length=20, choices=EXPERIENCE_LEVEL_CHOICES, default='intermediate', verbose_name='مستوى الخبرة')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, verbose_name='الحالة')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    class Meta:
        verbose_name = 'عون'
        verbose_name_plural = 'الأعوان'

    def __str__(self):
        return f"{self.full_name} - {self.rank} - {self.get_job_function_display()}"

    def can_drive(self):
        """أي عون يمكنه القيادة - بدون قيود"""
        return True

    def can_lead_crew(self):
        """أي عون يمكنه قيادة العدد - بدون قيود"""
        return True


class EquipmentCount(models.Model):
    """Model for equipment and vehicles count"""
    STATUS_CHOICES = (
        ('operational', 'تعمل'),
        ('broken', 'معطلة'),
        ('maintenance', 'تحت الصيانة')
    )

    daily_count = models.ForeignKey(DailyUnitCount, on_delete=models.CASCADE, related_name='equipment')
    serial_number = models.CharField(max_length=50, verbose_name='الرقم التسلسلي')
    equipment_type = models.CharField(max_length=100, verbose_name='نوع الوسيلة')
    radio_number = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم إشارة الراديو')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, verbose_name='الحالة')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    class Meta:
        verbose_name = 'وسيلة'
        verbose_name_plural = 'الوسائل'

    def __str__(self):
        return f"{self.equipment_type} - {self.serial_number}"


class TransferRecord(models.Model):
    """Model for tracking transfers of personnel or equipment"""
    TRANSFER_TYPE_CHOICES = (
        ('personnel', 'عون'),
        ('equipment', 'وسيلة')
    )

    transfer_type = models.CharField(max_length=20, choices=TRANSFER_TYPE_CHOICES, verbose_name='نوع التحويل')
    item_name = models.CharField(max_length=100, verbose_name='العنصر')
    from_unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, related_name='transfers_from', verbose_name='من وحدة')
    to_unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, related_name='transfers_to', verbose_name='إلى وحدة')
    transfer_date = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التحويل')
    transferred_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='تم التحويل بواسطة')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    class Meta:
        verbose_name = 'سجل التحويل'
        verbose_name_plural = 'سجلات التحويلات'
        ordering = ['-transfer_date']

    def __str__(self):
        return f"تحويل {self.item_name} من {self.from_unit.name} إلى {self.to_unit.name}"


# ===== نماذج جاهزية الوسائل المتقدمة =====

class VehicleRequirements(models.Model):
    """متطلبات العدد لكل نوع وسيلة"""
    vehicle_type = models.CharField(max_length=100, verbose_name='نوع الوسيلة')
    required_driver_count = models.IntegerField(default=1, verbose_name='عدد السائقين المطلوب')
    required_crew_chief_count = models.IntegerField(default=1, verbose_name='عدد رؤساء العدد المطلوب')
    required_agent_count = models.IntegerField(default=2, verbose_name='عدد الأعوان المطلوب')
    minimum_agent_count = models.IntegerField(default=1, verbose_name='الحد الأدنى للأعوان')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'متطلبات الوسيلة'
        verbose_name_plural = 'متطلبات الوسائل'
        unique_together = ['vehicle_type']

    def __str__(self):
        return f"{self.vehicle_type} - سائق:{self.required_driver_count} رئيس:{self.required_crew_chief_count} أعوان:{self.required_agent_count}"


class VehicleCrewAssignment(models.Model):
    """ربط الأعوان بالوسائل"""
    ROLE_CHOICES = [
        ('driver', 'سائق'),
        ('crew_chief', 'رئيس عدد'),
        ('agent', 'عون'),
    ]

    vehicle = models.ForeignKey('UnitEquipment', on_delete=models.CASCADE, related_name='crew_assignments', verbose_name='الوسيلة')
    personnel = models.ForeignKey('UnitPersonnel', on_delete=models.CASCADE, related_name='vehicle_assignments', verbose_name='العون')
    role = models.CharField(max_length=50, choices=ROLE_CHOICES, verbose_name='الدور')
    assignment_date = models.DateField(verbose_name='تاريخ التعيين')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'تعيين العون على الوسيلة'
        verbose_name_plural = 'تعيينات الأعوان على الوسائل'
        unique_together = ['vehicle', 'personnel', 'assignment_date']
        ordering = ['assignment_date', 'role']

    def __str__(self):
        return f"{self.personnel.full_name} - {self.get_role_display()} - {self.vehicle.equipment_type}"


class VehicleReadiness(models.Model):
    """حالة جاهزية الوسائل"""
    READINESS_STATUS_CHOICES = [
        ('ready', 'جاهز'),
        ('not_ready', 'غير جاهز'),
        ('manually_confirmed', 'مؤكد يدوياً'),
        ('under_maintenance', 'تحت الصيانة'),
    ]

    vehicle = models.ForeignKey('UnitEquipment', on_delete=models.CASCADE, related_name='readiness_records', verbose_name='الوسيلة')
    date = models.DateField(verbose_name='التاريخ')
    is_automatically_ready = models.BooleanField(default=False, verbose_name='جاهز تلقائياً')
    is_manually_confirmed = models.BooleanField(default=False, verbose_name='مؤكد يدوياً')
    confirmed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='تم التأكيد بواسطة')
    confirmation_reason = models.TextField(blank=True, null=True, verbose_name='سبب التأكيد اليدوي')
    missing_roles = models.JSONField(default=list, verbose_name='الأدوار المفقودة')
    readiness_score = models.IntegerField(default=0, verbose_name='نسبة الجاهزية')
    status = models.CharField(max_length=20, choices=READINESS_STATUS_CHOICES, default='not_ready', verbose_name='الحالة')
    last_check_time = models.DateTimeField(auto_now=True, verbose_name='آخر فحص')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'جاهزية الوسيلة'
        verbose_name_plural = 'جاهزية الوسائل'
        unique_together = ['vehicle', 'date']
        ordering = ['-date', 'vehicle']

    def __str__(self):
        return f"{self.vehicle.equipment_type} - {self.date} - {self.get_status_display()}"

    def calculate_readiness_score(self):
        """حساب نسبة الجاهزية - مبسط بدون قيود معقدة"""
        try:
            # الحصول على الأعوان المعينين لهذا اليوم
            assignments = VehicleCrewAssignment.objects.filter(
                vehicle=self.vehicle,
                assignment_date=self.date,
                is_active=True
            )

            # حساب عدد الأعوان المعينين
            total_assigned = assignments.count()

            # حساب بسيط: إذا كان هناك أعوان معينين، الوسيلة جاهزة
            if total_assigned > 0:
                score = 100  # جاهز بنسبة 100% إذا كان هناك أي عون معين
                has_minimum = True
            else:
                score = 0
                has_minimum = False

            # تحديث الحالة - فقط إذا كانت الوسيلة تعمل ولديها أعوان
            if has_minimum and hasattr(self.vehicle, 'status') and self.vehicle.status == 'operational':
                self.is_automatically_ready = True
                self.status = 'ready'
            else:
                self.is_automatically_ready = False
                if not self.is_manually_confirmed:
                    self.status = 'not_ready'

            # تحديث الأدوار المفقودة (مبسط)
            missing = []
            if total_assigned == 0:
                missing.append("لا يوجد أعوان معينين")

            self.missing_roles = missing
            self.readiness_score = score

            return score

        except Exception as e:
            # في حالة الخطأ، تعيين النسبة إلى 0
            self.readiness_score = 0
            self.status = 'not_ready'
            return 0

    def confirm_manually(self, user, reason):
        """تأكيد الجاهزية يدوياً"""
        self.is_manually_confirmed = True
        self.confirmed_by = user
        self.confirmation_reason = reason
        self.status = 'manually_confirmed'
        self.save()

    def get_readiness_color(self):
        """الحصول على لون الجاهزية"""
        if self.status == 'ready':
            return 'success'
        elif self.status == 'manually_confirmed':
            return 'warning'
        elif self.status == 'under_maintenance':
            return 'info'
        else:
            return 'danger'


# نماذج إدارة الرتب والمناصب
class PersonnelRank(models.Model):
    """نموذج الرتب العسكرية"""
    name = models.CharField(max_length=100, unique=True, verbose_name='اسم الرتبة')
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')

    class Meta:
        verbose_name = 'رتبة عسكرية'
        verbose_name_plural = 'الرتب العسكرية'
        ordering = ['name']

    def __str__(self):
        return self.name


class PersonnelPosition(models.Model):
    """نموذج المناصب الوظيفية"""
    name = models.CharField(max_length=100, unique=True, verbose_name='اسم المنصب')
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')

    class Meta:
        verbose_name = 'منصب وظيفي'
        verbose_name_plural = 'المناصب الوظيفية'
        ordering = ['name']

    def __str__(self):
        return self.name


# نماذج البيانات المستمرة للوحدة
class UnitPersonnel(models.Model):
    """نموذج الأعوان المستمرين في الوحدة"""
    GENDER_CHOICES = (
        ('male', 'ذكر'),
        ('female', 'أنثى'),
    )

    WORK_SYSTEM_CHOICES = (
        ('24_hours', 'نظام 24 ساعة'),
        ('8_hours', 'نظام 8 ساعات'),
    )

    SHIFT_CHOICES = (
        ('shift_1', 'الفرقة الأولى'),
        ('shift_2', 'الفرقة الثانية'),
        ('shift_3', 'الفرقة الثالثة'),
    )

    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')

    # الحقول الإجبارية المطلوبة
    registration_number = models.CharField(max_length=20, unique=True, verbose_name='رقم التسجيل')
    first_name = models.CharField(max_length=50, verbose_name='الاسم')
    last_name = models.CharField(max_length=50, verbose_name='اللقب')
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')

    # الحقول الشخصية المطلوبة
    birth_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')
    joining_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الالتحاق')
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, verbose_name='الجنس')
    phone_number = models.CharField(max_length=15, blank=True, null=True, verbose_name='رقم الهاتف')

    # الحقول المهنية المطلوبة
    rank = models.CharField(max_length=50, verbose_name='الرتبة')
    position = models.CharField(max_length=50, verbose_name='المنصب')

    # نظام العمل والفرقة المطلوبة
    work_system = models.CharField(max_length=20, choices=WORK_SYSTEM_CHOICES, default='24_hours', verbose_name='نظام العمل')
    assigned_shift = models.CharField(max_length=20, choices=SHIFT_CHOICES, blank=True, null=True, verbose_name='الفرقة المخصصة')

    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')

    class Meta:
        verbose_name = 'عون الوحدة'
        verbose_name_plural = 'أعوان الوحدة'
        unique_together = ['unit', 'registration_number']
        ordering = ['full_name']

    def __str__(self):
        return f"{self.full_name} - {self.unit.name}"

    # خصائص محسوبة للعمر وسنوات الخدمة
    @property
    def age(self):
        """حساب العمر تلقائياً من تاريخ الميلاد"""
        if self.birth_date:
            from datetime import date
            today = date.today()
            return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        return None

    @property
    def years_of_service(self):
        """حساب سنوات الخدمة تلقائياً من تاريخ الالتحاق"""
        if self.joining_date:
            from datetime import date
            today = date.today()
            return today.year - self.joining_date.year - ((today.month, today.day) < (self.joining_date.month, self.joining_date.day))
        return None

    def save(self, *args, **kwargs):
        """تحديث الاسم الكامل تلقائياً عند الحفظ"""
        # تحديث الاسم الكامل فقط إذا كانت القيم صحيحة وليست "غير محدد"
        if (self.first_name and self.last_name and
            self.first_name != 'غير محدد' and self.last_name != 'غير محدد'):
            self.full_name = f"{self.first_name} {self.last_name}"
        # إذا كان full_name فارغ أو غير صحيح، لا نغيره
        super().save(*args, **kwargs)

    def get_gender_icon(self):
        """أيقونة الجنس"""
        return 'fas fa-mars' if self.gender == 'male' else 'fas fa-venus'

    def get_work_system_badge_class(self):
        """فئة CSS لشارة نظام العمل"""
        return 'badge-primary' if self.work_system == '24_hours' else 'badge-info'

    def get_shift_display_arabic(self):
        """عرض اسم الفرقة بالعربية"""
        shift_names = {
            'shift_1': 'الفرقة الأولى',
            'shift_2': 'الفرقة الثانية',
            'shift_3': 'الفرقة الثالثة'
        }
        return shift_names.get(self.assigned_shift, 'غير محدد')

    def get_work_status_today(self, target_date=None):
        """تحديد حالة العمل لليوم (عمل/راحة) بناءً على الجدولة"""
        from django.utils import timezone

        if target_date is None:
            target_date = timezone.now().date()

        # للأعوان الذين يعملون 8 ساعات
        if self.work_system == '8_hours':
            # التحقق من أيام العمل (الأحد إلى الخميس)
            weekday = target_date.weekday()  # 0=Monday, 6=Sunday
            if weekday == 6 or weekday in [0, 1, 2, 3]:  # Sunday to Thursday
                return 'working'
            else:  # Friday, Saturday
                return 'resting'

        # للأعوان الذين يعملون 24 ساعة
        elif self.work_system == '24_hours':
            if not self.assigned_shift:
                return 'unassigned'

            # الحصول على الفرقة العاملة اليوم
            current_working_shift = ShiftSchedule.get_current_working_shift(self.unit, target_date)

            if current_working_shift == self.assigned_shift:
                return 'working'
            else:
                return 'resting'

        return 'unknown'

    def get_work_status_display(self, target_date=None):
        """عرض حالة العمل بالعربية"""
        status = self.get_work_status_today(target_date)
        status_map = {
            'working': 'قيد العمل',
            'resting': 'راحة',
            'unassigned': 'غير مخصص',
            'unknown': 'غير محدد'
        }
        return status_map.get(status, 'غير محدد')

    def get_work_status_badge_class(self, target_date=None):
        """الحصول على class CSS لحالة العمل"""
        status = self.get_work_status_today(target_date)
        class_map = {
            'working': 'badge-success',
            'resting': 'badge-secondary',
            'unassigned': 'badge-warning',
            'unknown': 'badge-light'
        }
        return class_map.get(status, 'badge-light')

    def get_gender_icon(self):
        """أيقونة الجنس"""
        return 'fas fa-mars' if self.gender == 'male' else 'fas fa-venus'

    def get_work_system_badge_class(self):
        """فئة CSS لشارة نظام العمل"""
        return 'badge-primary' if self.work_system == '24_hours' else 'badge-info'

    def get_shift_badge_class(self):
        """فئة CSS لشارة الفرقة"""
        shift_classes = {
            'shift_1': 'badge-primary',
            'shift_2': 'badge-success',
            'shift_3': 'badge-warning'
        }
        return shift_classes.get(self.assigned_shift, 'badge-secondary')


class UnitEquipment(models.Model):
    """نموذج الوسائل المستمرة في الوحدة"""
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')
    serial_number = models.CharField(max_length=50, verbose_name='الرقم التسلسلي')
    equipment_type = models.CharField(max_length=100, verbose_name='نوع الوسيلة')
    radio_number = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم الراديو')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')

    class Meta:
        verbose_name = 'وسيلة الوحدة'
        verbose_name_plural = 'وسائل الوحدة'
        unique_together = ['unit', 'serial_number']
        ordering = ['equipment_type']

    def __str__(self):
        return f"{self.equipment_type} - {self.serial_number}"


class DailyPersonnelStatus(models.Model):
    """نموذج حالة العون اليومية"""
    STATUS_CHOICES = (
        ('present', 'حاضر'),
        ('absent', 'غائب'),
        ('on_mission', 'في مهمة'),
        ('standby', 'احتياطي'),
    )

    personnel = models.ForeignKey(UnitPersonnel, on_delete=models.CASCADE, verbose_name='العون')
    date = models.DateField(verbose_name='التاريخ')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='present', verbose_name='الحالة')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='حدث بواسطة')
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'حالة العون اليومية'
        verbose_name_plural = 'حالات الأعوان اليومية'
        unique_together = ['personnel', 'date']
        ordering = ['-date', 'personnel__full_name']

    def __str__(self):
        return f"{self.personnel.full_name} - {self.date} - {self.get_status_display()}"


class DailyEquipmentStatus(models.Model):
    """نموذج حالة الوسيلة اليومية"""
    STATUS_CHOICES = (
        ('operational', 'تعمل'),
        ('broken', 'معطلة'),
        ('maintenance', 'تحت الصيانة'),
    )

    equipment = models.ForeignKey(UnitEquipment, on_delete=models.CASCADE, verbose_name='الوسيلة')
    date = models.DateField(verbose_name='التاريخ')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='operational', verbose_name='الحالة')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='حدث بواسطة')
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'حالة الوسيلة اليومية'
        verbose_name_plural = 'حالات الوسائل اليومية'
        unique_together = ['equipment', 'date']
        ordering = ['-date', 'equipment__equipment_type']

    def __str__(self):
        return f"{self.equipment.equipment_type} - {self.date} - {self.get_status_display()}"


# نموذج أنواع الوسائل المخصصة
class EquipmentType(models.Model):
    """نموذج أنواع الوسائل المخصصة"""
    name = models.CharField(max_length=100, unique=True, verbose_name='اسم نوع الوسيلة')
    category = models.CharField(max_length=50, blank=True, null=True, verbose_name='الفئة')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')

    class Meta:
        verbose_name = 'نوع وسيلة'
        verbose_name_plural = 'أنواع الوسائل'
        ordering = ['category', 'name']

    def __str__(self):
        return self.name


# ========================================
# نماذج نظام Morning Check System الجديد
# ========================================

class WorkShift(models.Model):
    """نموذج الفرق العاملة (نظام 24 ساعة الجديد)"""
    SHIFT_TYPES = (
        ('24_hours', 'نظام 24 ساعة'),
        ('8_hours', 'نظام 8 ساعات'),
    )

    # الفرق الثلاث الثابتة للنظام الجديد
    SHIFT_NAMES = (
        ('shift_1', 'الفرقة الأولى'),
        ('shift_2', 'الفرقة الثانية'),
        ('shift_3', 'الفرقة الثالثة'),
        # الفرق القديمة للتوافق
        ('A', 'فصيلة A'),
        ('B', 'فصيلة B'),
        ('C', 'فصيلة C'),
        ('morning', 'فترة صباحية'),
        ('evening', 'فترة مسائية'),
        ('night', 'فترة ليلية'),
    )

    # ألوان مميزة للفرق الثلاث
    SHIFT_COLORS = (
        ('shift_1', '#007bff'),  # أزرق
        ('shift_2', '#28a745'),  # أخضر
        ('shift_3', '#fd7e14'),  # برتقالي
    )

    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')
    name = models.CharField(max_length=20, choices=SHIFT_NAMES, verbose_name='اسم الفرقة')
    shift_type = models.CharField(max_length=20, choices=SHIFT_TYPES, default='24_hours', verbose_name='نوع النظام')
    color_code = models.CharField(max_length=7, blank=True, null=True, verbose_name='رمز اللون')
    description = models.TextField(blank=True, null=True, verbose_name='وصف الفرقة')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')

    class Meta:
        verbose_name = 'فرقة عمل'
        verbose_name_plural = 'فرق العمل'
        unique_together = ['unit', 'name']
        ordering = ['unit', 'shift_type', 'name']

    def __str__(self):
        return f"{self.unit.name} - {self.get_name_display()}"

    def get_color_code(self):
        """إرجاع رمز اللون للفرقة"""
        if self.color_code:
            return self.color_code

        # الألوان الافتراضية للفرق الثلاث
        default_colors = {
            'shift_1': '#007bff',  # أزرق
            'shift_2': '#28a745',  # أخضر
            'shift_3': '#fd7e14',  # برتقالي
        }
        return default_colors.get(self.name, '#6c757d')  # رمادي افتراضي

    def get_personnel_count(self):
        """إرجاع عدد الأعوان في الفرقة"""
        return UnitPersonnel.objects.filter(
            unit=self.unit,
            assigned_shift=self.name,
            is_active=True
        ).count()


class ShiftPersonnel(models.Model):
    """ربط الأعوان بالفرق"""
    shift = models.ForeignKey(WorkShift, on_delete=models.CASCADE, related_name='personnel', verbose_name='الفرقة')
    personnel = models.ForeignKey(UnitPersonnel, on_delete=models.CASCADE, related_name='shifts', verbose_name='العون')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    assigned_date = models.DateField(auto_now_add=True, verbose_name='تاريخ التعيين')
    assigned_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='عين بواسطة')

    class Meta:
        verbose_name = 'عون في فرقة'
        verbose_name_plural = 'الأعوان في الفرق'
        unique_together = ['shift', 'personnel']
        ordering = ['shift', 'personnel__full_name']

    def __str__(self):
        return f"{self.personnel.full_name} - {self.shift.get_name_display()}"


class DailyShiftSchedule(models.Model):
    """جدولة الفرق اليومية"""
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')
    date = models.DateField(verbose_name='التاريخ')
    active_shift = models.ForeignKey(WorkShift, on_delete=models.CASCADE, verbose_name='الفرقة العاملة')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'جدولة الفرق اليومية'
        verbose_name_plural = 'جدولة الفرق اليومية'
        unique_together = ['unit', 'date']
        ordering = ['-date', 'unit']

    def __str__(self):
        return f"{self.unit.name} - {self.date} - {self.active_shift.get_name_display()}"


class EightHourPersonnel(models.Model):
    """نموذج الأعوان العاملين بنظام 8 ساعات"""
    WORK_PERIODS = (
        ('morning', 'فترة صباحية (08:00 - 16:00)'),
        ('evening', 'فترة مسائية (16:00 - 00:00)'),
        ('night', 'فترة ليلية (00:00 - 08:00)'),
    )

    TASK_TYPES = (
        ('administrative', 'عمل إداري'),
        ('maintenance', 'صيانة'),
        ('communications', 'اتصالات'),
        ('storage', 'مخزن'),
        ('security', 'أمن'),
        ('support', 'دعم'),
        ('technical', 'تقني'),
    )

    GENDER_CHOICES = (
        ('male', 'ذكر'),
        ('female', 'أنثى'),
    )

    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')

    # البيانات الشخصية (بدلاً من الربط مع UnitPersonnel)
    personnel_registration_number = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم التسجيل')
    full_name = models.CharField(max_length=100, blank=True, null=True, verbose_name='الاسم الكامل')
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, blank=True, null=True, verbose_name='الجنس')
    age = models.PositiveIntegerField(blank=True, null=True, verbose_name='العمر')
    phone_number = models.CharField(max_length=15, blank=True, null=True, verbose_name='رقم الهاتف')

    # بيانات العمل
    date = models.DateField(verbose_name='التاريخ')
    work_period = models.CharField(max_length=20, choices=WORK_PERIODS, verbose_name='فترة العمل')
    task_type = models.CharField(max_length=20, choices=TASK_TYPES, verbose_name='نوع المهمة')
    task_description = models.TextField(blank=True, null=True, verbose_name='وصف المهمة')
    start_time = models.TimeField(verbose_name='وقت البداية')
    end_time = models.TimeField(verbose_name='وقت النهاية')
    is_present = models.BooleanField(default=True, verbose_name='حاضر')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'عون نظام 8 ساعات'
        verbose_name_plural = 'أعوان نظام 8 ساعات'
        ordering = ['-date', 'work_period', 'full_name']

    def __str__(self):
        return f"{self.full_name} - {self.date} - {self.get_work_period_display()}"


class ShiftSchedule(models.Model):
    """نموذج جدولة الفرق العاملة"""
    SHIFT_CHOICES = (
        ('shift_1', 'الفرقة الأولى (A)'),
        ('shift_2', 'الفرقة الثانية (B)'),
        ('shift_3', 'الفرقة الثالثة (C)'),
    )

    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')
    working_shift = models.CharField(max_length=20, choices=SHIFT_CHOICES, verbose_name='الفرقة العاملة')
    start_datetime = models.DateTimeField(verbose_name='بداية العمل')
    end_datetime = models.DateTimeField(verbose_name='نهاية العمل')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')

    class Meta:
        verbose_name = 'جدولة الفرقة'
        verbose_name_plural = 'جدولة الفرق'
        ordering = ['-start_datetime']

    def __str__(self):
        return f"{self.unit.name} - {self.get_working_shift_display()} - {self.start_datetime.strftime('%Y-%m-%d %H:%M')}"

    @classmethod
    def get_current_working_shift(cls, unit, datetime_obj=None):
        """الحصول على الفرقة العاملة حالياً"""
        from django.utils import timezone
        if datetime_obj is None:
            datetime_obj = timezone.now()

        current_schedule = cls.objects.filter(
            unit=unit,
            start_datetime__lte=datetime_obj,
            end_datetime__gte=datetime_obj,
            is_active=True
        ).first()

        return current_schedule.working_shift if current_schedule else None

    @classmethod
    def create_shift_cycle(cls, unit, start_date, created_by):
        """إنشاء دورة فرق لمدة أسبوع"""
        from datetime import datetime, timedelta
        from django.utils import timezone

        # بداية الدورة: 8 صباحاً
        current_start = datetime.combine(start_date, datetime.min.time().replace(hour=8))
        current_start = timezone.make_aware(current_start)

        shifts = ['shift_1', 'shift_2', 'shift_3']
        shift_index = 0

        # إنشاء جدولة لمدة 21 يوم (7 دورات × 3 أيام لكل فرقة)
        for day in range(21):
            shift_start = current_start + timedelta(days=day)
            shift_end = shift_start + timedelta(hours=24)

            cls.objects.create(
                unit=unit,
                working_shift=shifts[shift_index],
                start_datetime=shift_start,
                end_datetime=shift_end,
                created_by=created_by
            )

            # تغيير الفرقة كل يوم (نظام 24/24 بدلاً من 24/48)
            shift_index = (shift_index + 1) % 3

    @classmethod
    def create_monthly_schedule(cls, unit, year, month, created_by):
        """إنشاء جدولة شهرية للفرق"""
        from datetime import datetime, timedelta
        from django.utils import timezone
        import calendar

        # الحصول على عدد أيام الشهر
        days_in_month = calendar.monthrange(year, month)[1]

        # بداية الشهر
        start_date = datetime(year, month, 1)

        shifts = ['shift_1', 'shift_2', 'shift_3']
        shift_index = 0

        # إنشاء جدولة لكل يوم في الشهر
        for day in range(1, days_in_month + 1):
            current_date = datetime(year, month, day)
            shift_start = timezone.make_aware(current_date.replace(hour=8))
            shift_end = shift_start + timedelta(hours=24)

            # حذف الجدولة الموجودة لهذا التاريخ
            cls.objects.filter(
                unit=unit,
                start_datetime__date=current_date.date()
            ).delete()

            # إنشاء جدولة جديدة
            cls.objects.create(
                unit=unit,
                working_shift=shifts[shift_index],
                start_datetime=shift_start,
                end_datetime=shift_end,
                created_by=created_by,
                is_active=True
            )

            # تغيير الفرقة كل يوم
            shift_index = (shift_index + 1) % 3

        return f"تم إنشاء جدولة شهرية لـ {days_in_month} يوم"

    @classmethod
    def create_yearly_schedule(cls, unit, year, created_by):
        """إنشاء جدولة سنوية للفرق"""
        from datetime import datetime, timedelta
        from django.utils import timezone
        import calendar

        shifts = ['shift_1', 'shift_2', 'shift_3']
        shift_index = 0
        total_days = 0

        # إنشاء جدولة لكل شهر في السنة
        for month in range(1, 13):
            days_in_month = calendar.monthrange(year, month)[1]

            for day in range(1, days_in_month + 1):
                current_date = datetime(year, month, day)
                shift_start = timezone.make_aware(current_date.replace(hour=8))
                shift_end = shift_start + timedelta(hours=24)

                # حذف الجدولة الموجودة لهذا التاريخ
                cls.objects.filter(
                    unit=unit,
                    start_datetime__date=current_date.date()
                ).delete()

                # إنشاء جدولة جديدة
                cls.objects.create(
                    unit=unit,
                    working_shift=shifts[shift_index],
                    start_datetime=shift_start,
                    end_datetime=shift_end,
                    created_by=created_by,
                    is_active=True
                )

                # تغيير الفرقة كل يوم
                shift_index = (shift_index + 1) % 3
                total_days += 1

        return f"تم إنشاء جدولة سنوية لـ {total_days} يوم في سنة {year}"

    @classmethod
    def create_multi_year_schedule(cls, unit, start_year, end_year, created_by):
        """إنشاء جدولة لعدة سنوات"""
        from datetime import datetime, timedelta
        from django.utils import timezone
        import calendar

        shifts = ['shift_1', 'shift_2', 'shift_3']
        shift_index = 0
        total_days = 0
        years_created = []

        # إنشاء جدولة لكل سنة في النطاق المحدد
        for year in range(start_year, end_year + 1):
            year_days = 0

            for month in range(1, 13):
                days_in_month = calendar.monthrange(year, month)[1]

                for day in range(1, days_in_month + 1):
                    current_date = datetime(year, month, day)
                    shift_start = timezone.make_aware(current_date.replace(hour=8))
                    shift_end = shift_start + timedelta(hours=24)

                    # حذف الجدولة الموجودة لهذا التاريخ
                    cls.objects.filter(
                        unit=unit,
                        start_datetime__date=current_date.date()
                    ).delete()

                    # إنشاء جدولة جديدة
                    cls.objects.create(
                        unit=unit,
                        working_shift=shifts[shift_index],
                        start_datetime=shift_start,
                        end_datetime=shift_end,
                        created_by=created_by,
                        is_active=True
                    )

                    # تغيير الفرقة كل يوم
                    shift_index = (shift_index + 1) % 3
                    total_days += 1
                    year_days += 1

            years_created.append(f"{year} ({year_days} يوم)")

        return {
            'message': f"تم إنشاء جدولة شاملة لـ {total_days} يوم",
            'years': years_created,
            'total_days': total_days,
            'start_year': start_year,
            'end_year': end_year
        }

    @classmethod
    def get_working_shift_for_date(cls, unit, date_obj):
        """الحصول على الفرقة العاملة لتاريخ محدد"""
        from django.utils import timezone
        from datetime import datetime, time

        # تحويل التاريخ إلى datetime مع الساعة 8 صباحاً
        if isinstance(date_obj, datetime):
            target_datetime = date_obj
        else:
            target_datetime = timezone.make_aware(datetime.combine(date_obj, time(8, 0)))

        schedule = cls.objects.filter(
            unit=unit,
            start_datetime__lte=target_datetime,
            end_datetime__gt=target_datetime,
            is_active=True
        ).first()

        return schedule.working_shift if schedule else None

    def get_working_shift_display_arabic(self):
        """عرض اسم الفرقة العاملة بالعربية"""
        shift_names = {
            'shift_1': 'الفرقة الأولى',
            'shift_2': 'الفرقة الثانية',
            'shift_3': 'الفرقة الثالثة',
        }
        return shift_names.get(self.working_shift, 'غير محدد')

    @classmethod
    def get_current_working_shift(cls, unit, target_date=None):
        """الحصول على الفرقة العاملة حالياً"""
        from django.utils import timezone
        from datetime import datetime, time

        if target_date is None:
            target_date = timezone.now().date()

        # تحويل التاريخ إلى datetime مع الساعة 8 صباحاً
        target_datetime = timezone.make_aware(datetime.combine(target_date, time(8, 0)))

        schedule = cls.objects.filter(
            unit=unit,
            start_datetime__lte=target_datetime,
            end_datetime__gt=target_datetime,
            is_active=True
        ).first()

        return schedule.working_shift if schedule else None


class VehicleInterventionStatus(models.Model):
    """نموذج حالة الوسيلة في التدخل"""
    STATUS_CHOICES = [
        ('available', 'متاحة'),
        ('in_intervention', 'في تدخل'),
        ('returning', 'في طريق العودة'),
        ('maintenance', 'صيانة'),
        ('out_of_service', 'خارج الخدمة'),
    ]

    vehicle = models.ForeignKey(UnitEquipment, on_delete=models.CASCADE, related_name='intervention_status', verbose_name='الوسيلة')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='available', verbose_name='الحالة')
    date = models.DateField(auto_now_add=True, verbose_name='التاريخ')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')

    class Meta:
        verbose_name = 'حالة الوسيلة في التدخل'
        verbose_name_plural = 'حالات الوسائل في التدخل'
        unique_together = ['vehicle', 'date']

    def __str__(self):
        return f"{self.vehicle.equipment_type} - {self.get_status_display()}"

class PersonnelTransfer(models.Model):
    """نموذج تحويل الأعوان بين الفرق"""
    TRANSFER_STATUS_CHOICES = (
        ('pending', 'في الانتظار'),
        ('approved', 'موافق عليه'),
        ('rejected', 'مرفوض'),
        ('completed', 'مكتمل'),
    )

    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')
    personnel = models.ForeignKey(UnitPersonnel, on_delete=models.CASCADE, verbose_name='العون')
    from_shift = models.CharField(max_length=20, choices=UnitPersonnel.SHIFT_CHOICES, verbose_name='من الفرقة')
    to_shift = models.CharField(max_length=20, choices=UnitPersonnel.SHIFT_CHOICES, verbose_name='إلى الفرقة')
    transfer_reason = models.TextField(verbose_name='سبب التحويل')
    transfer_date = models.DateField(verbose_name='تاريخ التحويل')
    status = models.CharField(max_length=20, choices=TRANSFER_STATUS_CHOICES, default='pending', verbose_name='حالة التحويل')

    # معلومات الإنشاء والموافقة
    requested_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='requested_transfers', verbose_name='طلب بواسطة')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_transfers', verbose_name='وافق عليه')

    # ملاحظات إضافية
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'تحويل عون'
        verbose_name_plural = 'تحويلات الأعوان'
        ordering = ['-created_at']

    def __str__(self):
        return f"تحويل {self.personnel.full_name} من {self.get_from_shift_display()} إلى {self.get_to_shift_display()}"

    def get_from_shift_display(self):
        """إرجاع اسم الفرقة المصدر بالعربية"""
        shift_names = {
            'shift_1': 'الفرقة الأولى',
            'shift_2': 'الفرقة الثانية',
            'shift_3': 'الفرقة الثالثة',
        }
        return shift_names.get(self.from_shift, 'غير محدد')

    def get_to_shift_display(self):
        """إرجاع اسم الفرقة المستهدفة بالعربية"""
        shift_names = {
            'shift_1': 'الفرقة الأولى',
            'shift_2': 'الفرقة الثانية',
            'shift_3': 'الفرقة الثالثة',
        }
        return shift_names.get(self.to_shift, 'غير محدد')





class MonthlySchedule(models.Model):
    """نموذج الجدولة الشهرية للفرق"""
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')
    year = models.IntegerField(verbose_name='السنة')
    month = models.IntegerField(verbose_name='الشهر')
    schedule_data = models.JSONField(verbose_name='بيانات الجدولة')  # تخزين الجدولة كـ JSON
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشأ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'جدولة شهرية'
        verbose_name_plural = 'الجدولة الشهرية'
        unique_together = ['unit', 'year', 'month']

    def __str__(self):
        return f'{self.unit.name} - {self.year}/{self.month:02d}'


class WorkingHours(models.Model):
    """نموذج ساعات العمل للأنظمة المختلفة"""
    WORK_SYSTEM_CHOICES = (
        ('24_hours', 'نظام 24 ساعة'),
        ('8_hours', 'نظام 8 ساعات'),
    )

    WORK_DAYS_CHOICES = (
        ('all_week', 'جميع أيام الأسبوع'),
        ('sunday_to_thursday', 'الأحد إلى الخميس'),
        ('custom', 'مخصص'),
    )

    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')
    work_system = models.CharField(max_length=20, choices=WORK_SYSTEM_CHOICES, verbose_name='نظام العمل')
    start_time = models.TimeField(verbose_name='وقت البداية')
    end_time = models.TimeField(verbose_name='وقت النهاية')
    work_days = models.CharField(max_length=20, choices=WORK_DAYS_CHOICES, default='all_week', verbose_name='أيام العمل')
    is_overnight = models.BooleanField(default=False, verbose_name='عمل ليلي')  # للنظام 24 ساعة
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    class Meta:
        verbose_name = 'ساعات العمل'
        verbose_name_plural = 'ساعات العمل'

    def __str__(self):
        return f'{self.unit.name} - {self.get_work_system_display()}'


class ReadinessAlert(models.Model):
    """نموذج تنبيهات الجاهزية"""
    ALERT_TYPES = (
        ('personnel_shortage', 'نقص في الأعوان'),
        ('vehicle_shortage', 'نقص في الوسائل'),
        ('shift_incomplete', 'فرقة ناقصة'),
        ('vehicle_not_ready', 'وسيلة غير جاهزة'),
        ('manual_confirmation_needed', 'تحتاج تأكيد يدوي'),
    )

    PRIORITY_LEVELS = (
        ('low', 'منخفض'),
        ('medium', 'متوسط'),
        ('high', 'عالي'),
        ('critical', 'حرج'),
    )

    STATUS_CHOICES = (
        ('active', 'نشط'),
        ('acknowledged', 'تم الاطلاع'),
        ('resolved', 'تم الحل'),
        ('dismissed', 'تم التجاهل'),
    )

    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')
    alert_type = models.CharField(max_length=30, choices=ALERT_TYPES, verbose_name='نوع التنبيه')
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium', verbose_name='الأولوية')
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='active', verbose_name='الحالة')
    title = models.CharField(max_length=200, verbose_name='العنوان')
    message = models.TextField(verbose_name='الرسالة')
    date = models.DateField(verbose_name='التاريخ')
    created_at = models.DateTimeField(auto_now_add=True)
    acknowledged_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='تم الاطلاع بواسطة')
    acknowledged_at = models.DateTimeField(null=True, blank=True, verbose_name='وقت الاطلاع')
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_alerts', verbose_name='تم الحل بواسطة')
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name='وقت الحل')
    resolution_notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات الحل')

    class Meta:
        verbose_name = 'تنبيه جاهزية'
        verbose_name_plural = 'تنبيهات الجاهزية'
        ordering = ['-created_at', '-priority']

    def __str__(self):
        return f"{self.unit.name} - {self.get_alert_type_display()} - {self.date}"

    def get_priority_color(self):
        """الحصول على لون الأولوية"""
        colors = {
            'low': 'info',
            'medium': 'warning',
            'high': 'danger',
            'critical': 'dark'
        }
        return colors.get(self.priority, 'secondary')

    def get_status_color(self):
        """الحصول على لون الحالة"""
        colors = {
            'active': 'danger',
            'acknowledged': 'warning',
            'resolved': 'success',
            'dismissed': 'secondary'
        }
        return colors.get(self.status, 'secondary')


# ========================================
# Daily Interventions System Models



























class MorningCheckSummary(models.Model):
    """ملخص التحقق الصباحي"""
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')
    date = models.DateField(verbose_name='التاريخ')
    active_shift = models.ForeignKey(WorkShift, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='الفرقة العاملة')

    # إحصائيات الأعوان
    total_personnel = models.IntegerField(default=0, verbose_name='إجمالي الأعوان')
    present_personnel = models.IntegerField(default=0, verbose_name='الأعوان الحاضرين')
    absent_personnel = models.IntegerField(default=0, verbose_name='الأعوان الغائبين')
    on_mission_personnel = models.IntegerField(default=0, verbose_name='الأعوان في مهمة')

    # إحصائيات الوسائل
    total_vehicles = models.IntegerField(default=0, verbose_name='إجمالي الوسائل')
    ready_vehicles = models.IntegerField(default=0, verbose_name='الوسائل الجاهزة')
    not_ready_vehicles = models.IntegerField(default=0, verbose_name='الوسائل غير الجاهزة')
    under_maintenance_vehicles = models.IntegerField(default=0, verbose_name='الوسائل تحت الصيانة')

    # إحصائيات التوزيع
    assigned_personnel = models.IntegerField(default=0, verbose_name='الأعوان المعينين')
    unassigned_personnel = models.IntegerField(default=0, verbose_name='الأعوان غير المعينين')

    # حالة الجاهزية العامة
    overall_readiness_score = models.IntegerField(default=0, verbose_name='نسبة الجاهزية العامة')
    is_fully_ready = models.BooleanField(default=False, verbose_name='جاهز بالكامل')

    # التوقيتات
    check_completed_at = models.DateTimeField(null=True, blank=True, verbose_name='وقت إتمام التحقق')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'ملخص التحقق الصباحي'
        verbose_name_plural = 'ملخصات التحقق الصباحي'
        unique_together = ['unit', 'date']
        ordering = ['-date', 'unit']

    def __str__(self):
        return f"ملخص {self.unit.name} - {self.date}"

    def calculate_readiness_score(self):
        """حساب نسبة الجاهزية العامة"""
        if self.total_vehicles == 0:
            return 0

        # حساب نسبة الوسائل الجاهزة
        vehicle_readiness = (self.ready_vehicles / self.total_vehicles) * 100 if self.total_vehicles > 0 else 0

        # حساب نسبة الأعوان الحاضرين
        personnel_readiness = (self.present_personnel / self.total_personnel) * 100 if self.total_personnel > 0 else 0

        # حساب نسبة التوزيع
        assignment_readiness = (self.assigned_personnel / self.present_personnel) * 100 if self.present_personnel > 0 else 0

        # المتوسط المرجح
        overall_score = (vehicle_readiness * 0.4 + personnel_readiness * 0.4 + assignment_readiness * 0.2)

        self.overall_readiness_score = int(overall_score)
        self.is_fully_ready = overall_score >= 90

        return self.overall_readiness_score

    def get_readiness_color(self):
        """الحصول على لون نسبة الجاهزية"""
        if self.overall_readiness_score >= 90:
            return 'success'
        elif self.overall_readiness_score >= 70:
            return 'warning'
        elif self.overall_readiness_score >= 50:
            return 'info'
        else:
            return 'danger'


# ==================== نماذج التدخلات اليومية المتقدمة ====================

class DailyIntervention(models.Model):
    """نموذج التدخلات اليومية المتقدمة"""

    INTERVENTION_TYPES = [
        ('medical', 'إجلاء صحي'),
        ('accident', 'حادث مرور'),
        ('agricultural-fire', 'حريق محاصيل زراعية'),
        ('building-fire', 'حرائق البنايات والمؤسسات'),
        ('other', 'عمليات مختلفة'),
    ]

    STATUS_CHOICES = [
        ('initial_report', 'بلاغ أولي'),
        ('reconnaissance', 'قيد التعرف'),
        ('intervention', 'عملية تدخل'),
        ('completed', 'منتهية'),
        ('escalated', 'مصعدة لكارثة كبرى'),
    ]

    CONTACT_SOURCES = [
        ('citizen', 'مواطن'),
        ('police', 'الشرطة'),
        ('gendarmerie', 'الدرك الوطني'),
        ('army', 'الجيش الوطني الشعبي'),
        ('forest', 'مصالح الغابات'),
        ('customs', 'الجمارك'),
        ('local-authorities', 'السلطات المحلية'),
        ('other', 'أخرى'),
    ]

    CONTACT_TYPES = [
        ('phone', 'هاتف'),
        ('radio', 'راديو'),
        ('unit-request', 'وحدة تطلب الدعم'),
        ('direct', 'مباشر'),
    ]

    # معلومات أساسية
    unit = models.ForeignKey(InterventionUnit, on_delete=models.CASCADE, verbose_name='الوحدة')
    intervention_number = models.CharField(max_length=20, unique=True, verbose_name='رقم التدخل')
    intervention_type = models.CharField(max_length=30, choices=INTERVENTION_TYPES, verbose_name='نوع التدخل')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='initial_report', verbose_name='الحالة')

    # بيانات البلاغ الأولي
    exit_time = models.TimeField(verbose_name='ساعة الخروج')
    intervention_location = models.CharField(max_length=200, verbose_name='مكان التدخل')
    contact_source = models.CharField(max_length=30, choices=CONTACT_SOURCES, verbose_name='الجهة المتصلة')
    contact_type = models.CharField(max_length=20, choices=CONTACT_TYPES, verbose_name='نوع الاتصال')
    phone_number = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم الهاتف')
    additional_notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات إضافية')

    # بيانات عملية التعرف
    arrival_time = models.TimeField(blank=True, null=True, verbose_name='ساعة الوصول')
    reconnaissance_notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات التعرف')
    support_requested = models.BooleanField(default=False, verbose_name='تم طلب الدعم')

    # بيانات إنهاء المهمة
    end_time = models.TimeField(blank=True, null=True, verbose_name='ساعة الانتهاء')
    total_duration = models.CharField(max_length=20, blank=True, null=True, verbose_name='مدة التدخل الإجمالية')
    final_injured_count = models.IntegerField(default=0, verbose_name='عدد المسعفين النهائي')
    final_deaths_count = models.IntegerField(default=0, verbose_name='عدد الوفيات النهائي')
    final_notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات ختامية')

    # معلومات النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='أنشئ بواسطة')

    class Meta:
        verbose_name = 'تدخل يومي'
        verbose_name_plural = 'التدخلات اليومية'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.intervention_number} - {self.get_intervention_type_display()} - {self.get_status_display()}"

    def generate_intervention_number(self):
        """توليد رقم تدخل فريد"""
        from datetime import date
        today = date.today()
        count = DailyIntervention.objects.filter(created_at__date=today).count() + 1
        return f"{today.strftime('%Y%m%d')}-{count:03d}"

    def save(self, *args, **kwargs):
        if not self.intervention_number:
            self.intervention_number = self.generate_intervention_number()
        super().save(*args, **kwargs)


class InterventionVehicle(models.Model):
    """نموذج الوسائل المشاركة في التدخل"""
    intervention = models.ForeignKey(DailyIntervention, on_delete=models.CASCADE, related_name='vehicles', verbose_name='التدخل')
    vehicle = models.ForeignKey(UnitEquipment, on_delete=models.CASCADE, verbose_name='الوسيلة')
    is_primary = models.BooleanField(default=True, verbose_name='وسيلة أساسية')
    assigned_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعيين')

    class Meta:
        verbose_name = 'وسيلة التدخل'
        verbose_name_plural = 'وسائل التدخل'
        unique_together = ['intervention', 'vehicle']

    def __str__(self):
        return f"{self.intervention.intervention_number} - {self.vehicle.name}"
