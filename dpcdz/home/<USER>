from django.contrib import admin
from .models import (UserProfile, InterventionUnit, CoordinationCenterForestFire,
                     CoordinationCenterCropFire, DailyUnitCount, PersonnelCount,
                     EquipmentCount, TransferRecord, PersonnelRank, PersonnelPosition,
                     UnitPersonnel, UnitEquipment, DailyPersonnelStatus,
                     DailyEquipmentStatus, EquipmentType, WorkShift, ShiftPersonnel,
                     DailyShiftSchedule, EightHourPersonnel, ReadinessAlert,
                     MorningCheckSummary, VehicleCrewAssignment, VehicleReadiness,
                     PersonnelTransfer, VehicleInterventionStatus, DailyIntervention,
                     InterventionVehicle)

class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'get_wilaya_display', 'role')
    list_filter = ('role', 'wilaya')
    search_fields = ('user__username', 'wilaya')

class InterventionUnitAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'get_wilaya_display')
    list_filter = ('wilaya',)
    search_fields = ('code', 'name')

admin.site.register(UserProfile, UserProfileAdmin)
admin.site.register(InterventionUnit, InterventionUnitAdmin)

class CoordinationCenterForestFireAdmin(admin.ModelAdmin):
    list_display = ('telegram_number', 'date', 'intervening_unit', 'municipality', 'fire_control_status')
    list_filter = ('date', 'fire_control_status', 'intervening_unit')
    search_fields = ('telegram_number', 'municipality', 'location_name')

class CoordinationCenterCropFireAdmin(admin.ModelAdmin):
    list_display = ('telegram_number', 'date', 'intervening_unit', 'municipality', 'fire_control_status')
    list_filter = ('date', 'fire_control_status', 'intervening_unit')
    search_fields = ('telegram_number', 'municipality', 'location_name')

class DailyUnitCountAdmin(admin.ModelAdmin):
    list_display = ('unit', 'date', 'created_by', 'created_at')
    list_filter = ('date', 'unit__wilaya')
    search_fields = ('unit__name',)

class PersonnelCountAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'rank', 'position', 'status', 'daily_count')
    list_filter = ('status', 'rank', 'position')
    search_fields = ('full_name', 'registration_number')

class EquipmentCountAdmin(admin.ModelAdmin):
    list_display = ('equipment_type', 'serial_number', 'status', 'daily_count')
    list_filter = ('status', 'equipment_type')
    search_fields = ('serial_number', 'equipment_type')

class TransferRecordAdmin(admin.ModelAdmin):
    list_display = ('item_name', 'transfer_type', 'from_unit', 'to_unit', 'transfer_date', 'transferred_by')
    list_filter = ('transfer_type', 'transfer_date')
    search_fields = ('item_name',)

admin.site.register(CoordinationCenterForestFire, CoordinationCenterForestFireAdmin)
admin.site.register(CoordinationCenterCropFire, CoordinationCenterCropFireAdmin)
admin.site.register(DailyUnitCount, DailyUnitCountAdmin)
admin.site.register(PersonnelCount, PersonnelCountAdmin)
admin.site.register(EquipmentCount, EquipmentCountAdmin)
admin.site.register(TransferRecord, TransferRecordAdmin)

# إدارة الرتب والمناصب
class PersonnelRankAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_by', 'created_at')
    list_filter = ('created_at', 'created_by')
    search_fields = ('name',)
    ordering = ('name',)

class PersonnelPositionAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_by', 'created_at')
    list_filter = ('created_at', 'created_by')
    search_fields = ('name',)
    ordering = ('name',)

admin.site.register(PersonnelRank, PersonnelRankAdmin)
admin.site.register(PersonnelPosition, PersonnelPositionAdmin)

# إدارة البيانات المستمرة
class UnitPersonnelAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'registration_number', 'unit', 'gender', 'age', 'years_of_service', 'work_system', 'assigned_shift', 'is_active')
    list_filter = ('unit', 'gender', 'work_system', 'assigned_shift', 'rank', 'position', 'is_active', 'created_at')
    search_fields = ('full_name', 'first_name', 'last_name', 'registration_number', 'phone_number')
    ordering = ('unit', 'full_name')
    readonly_fields = ('created_at', 'full_name', 'age', 'years_of_service')

    fieldsets = (
        ('البيانات الأساسية', {
            'fields': ('unit', 'registration_number', 'first_name', 'last_name', 'full_name')
        }),
        ('البيانات الشخصية', {
            'fields': ('birth_date', 'joining_date', 'gender', 'phone_number', 'age', 'years_of_service')
        }),
        ('البيانات الوظيفية', {
            'fields': ('rank', 'position', 'work_system', 'assigned_shift')
        }),
        ('الحالة', {
            'fields': ('is_active', 'created_by', 'created_at')
        }),
    )

class UnitEquipmentAdmin(admin.ModelAdmin):
    list_display = ('equipment_type', 'serial_number', 'unit', 'radio_number', 'is_active', 'created_at')
    list_filter = ('unit', 'equipment_type', 'is_active', 'created_at')
    search_fields = ('equipment_type', 'serial_number', 'radio_number')
    ordering = ('unit', 'equipment_type')

class DailyPersonnelStatusAdmin(admin.ModelAdmin):
    list_display = ('personnel', 'date', 'status', 'updated_by', 'updated_at')
    list_filter = ('date', 'status', 'personnel__unit')
    search_fields = ('personnel__full_name', 'personnel__registration_number')
    ordering = ('-date', 'personnel__full_name')

class DailyEquipmentStatusAdmin(admin.ModelAdmin):
    list_display = ('equipment', 'date', 'status', 'updated_by', 'updated_at')
    list_filter = ('date', 'status', 'equipment__unit')
    search_fields = ('equipment__equipment_type', 'equipment__serial_number')
    ordering = ('-date', 'equipment__equipment_type')

class EquipmentTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'is_active', 'created_by', 'created_at')
    list_filter = ('category', 'is_active', 'created_at')
    search_fields = ('name', 'category')
    ordering = ('category', 'name')

admin.site.register(UnitPersonnel, UnitPersonnelAdmin)
admin.site.register(UnitEquipment, UnitEquipmentAdmin)
admin.site.register(DailyPersonnelStatus, DailyPersonnelStatusAdmin)
admin.site.register(DailyEquipmentStatus, DailyEquipmentStatusAdmin)
admin.site.register(EquipmentType, EquipmentTypeAdmin)

# Morning Check System Models
class WorkShiftAdmin(admin.ModelAdmin):
    list_display = ('unit', 'name', 'shift_type', 'is_active', 'created_by', 'created_at')
    list_filter = ('shift_type', 'name', 'is_active', 'unit')
    search_fields = ('unit__name', 'name')
    ordering = ('unit', 'shift_type', 'name')

class ShiftPersonnelAdmin(admin.ModelAdmin):
    list_display = ('shift', 'personnel', 'is_active', 'assigned_date', 'assigned_by')
    list_filter = ('shift__unit', 'shift__name', 'is_active', 'assigned_date')
    search_fields = ('personnel__full_name', 'shift__name')
    ordering = ('shift', 'personnel__full_name')

class DailyShiftScheduleAdmin(admin.ModelAdmin):
    list_display = ('unit', 'date', 'active_shift', 'created_by', 'created_at')
    list_filter = ('date', 'unit', 'active_shift__name')
    search_fields = ('unit__name', 'active_shift__name')
    ordering = ('-date', 'unit')

class EightHourPersonnelAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'personnel_registration_number', 'date', 'work_period', 'task_type', 'gender', 'age', 'is_present')
    list_filter = ('date', 'work_period', 'task_type', 'gender', 'is_present', 'unit')
    search_fields = ('full_name', 'personnel_registration_number', 'phone_number', 'task_description')
    ordering = ('-date', 'work_period', 'full_name')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('البيانات الشخصية', {
            'fields': ('unit', 'personnel_registration_number', 'full_name', 'gender', 'age', 'phone_number')
        }),
        ('بيانات العمل', {
            'fields': ('date', 'work_period', 'task_type', 'task_description', 'start_time', 'end_time')
        }),
        ('الحضور والملاحظات', {
            'fields': ('is_present', 'notes')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at')
        }),
    )

class ReadinessAlertAdmin(admin.ModelAdmin):
    list_display = ('unit', 'alert_type', 'priority', 'status', 'date', 'created_at')
    list_filter = ('alert_type', 'priority', 'status', 'date', 'unit')
    search_fields = ('title', 'message', 'unit__name')
    ordering = ('-created_at', '-priority')

class MorningCheckSummaryAdmin(admin.ModelAdmin):
    list_display = ('unit', 'date', 'overall_readiness_score', 'is_fully_ready', 'active_shift', 'created_by')
    list_filter = ('date', 'is_fully_ready', 'unit', 'active_shift')
    search_fields = ('unit__name',)
    ordering = ('-date', 'unit')

class VehicleCrewAssignmentAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'personnel', 'role', 'assignment_date', 'is_active')
    list_filter = ('role', 'assignment_date', 'is_active', 'vehicle__unit')
    search_fields = ('personnel__full_name', 'vehicle__equipment_type')
    ordering = ('-assignment_date', 'vehicle', 'role')

class VehicleReadinessAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'date', 'status', 'readiness_score', 'is_automatically_ready', 'is_manually_confirmed')
    list_filter = ('date', 'status', 'is_automatically_ready', 'is_manually_confirmed', 'vehicle__unit')
    search_fields = ('vehicle__equipment_type', 'vehicle__serial_number')
    ordering = ('-date', 'vehicle')

# Personnel Transfer Admin
class PersonnelTransferAdmin(admin.ModelAdmin):
    list_display = ('personnel', 'from_shift', 'to_shift', 'transfer_date', 'status', 'requested_by')
    list_filter = ('status', 'transfer_date', 'from_shift', 'to_shift', 'unit')
    search_fields = ('personnel__full_name', 'personnel__personnel_registration_number', 'transfer_reason')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)

# Register Morning Check System models
admin.site.register(WorkShift, WorkShiftAdmin)
admin.site.register(ShiftPersonnel, ShiftPersonnelAdmin)
admin.site.register(DailyShiftSchedule, DailyShiftScheduleAdmin)
admin.site.register(EightHourPersonnel, EightHourPersonnelAdmin)
admin.site.register(ReadinessAlert, ReadinessAlertAdmin)
admin.site.register(MorningCheckSummary, MorningCheckSummaryAdmin)
admin.site.register(VehicleCrewAssignment, VehicleCrewAssignmentAdmin)
admin.site.register(VehicleReadiness, VehicleReadinessAdmin)
admin.site.register(PersonnelTransfer, PersonnelTransferAdmin)

@admin.register(VehicleInterventionStatus)
class VehicleInterventionStatusAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'status', 'date', 'updated_at')
    list_filter = ('status', 'date')
    search_fields = ('vehicle__serial_number', 'vehicle__equipment_type')
    readonly_fields = ('date', 'updated_at')


# ==================== إدارة التدخلات اليومية ====================

class InterventionVehicleInline(admin.TabularInline):
    model = InterventionVehicle
    extra = 1
    fields = ('vehicle', 'is_primary')

@admin.register(DailyIntervention)
class DailyInterventionAdmin(admin.ModelAdmin):
    list_display = ('intervention_number', 'intervention_type', 'status', 'unit', 'exit_time', 'created_at')
    list_filter = ('intervention_type', 'status', 'unit', 'created_at')
    search_fields = ('intervention_number', 'intervention_location', 'phone_number')
    readonly_fields = ('intervention_number', 'created_at', 'updated_at')
    inlines = [InterventionVehicleInline]

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('intervention_number', 'unit', 'intervention_type', 'status', 'created_by')
        }),
        ('بيانات البلاغ الأولي', {
            'fields': ('exit_time', 'intervention_location', 'contact_source', 'contact_type', 'phone_number', 'additional_notes')
        }),
        ('بيانات عملية التعرف', {
            'fields': ('arrival_time', 'reconnaissance_notes', 'support_requested'),
            'classes': ('collapse',)
        }),
        ('بيانات إنهاء المهمة', {
            'fields': ('end_time', 'total_duration', 'final_injured_count', 'final_deaths_count', 'final_notes'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(InterventionVehicle)
class InterventionVehicleAdmin(admin.ModelAdmin):
    list_display = ('intervention', 'vehicle', 'is_primary', 'assigned_at')
    list_filter = ('is_primary', 'assigned_at')
    search_fields = ('intervention__intervention_number', 'vehicle__name')


